/**
 * Simple test for SNG Tournament Ranking Logic (without database)
 * 
 * This script tests the ranking calculation and reward distribution logic
 */

const consts = require('../app/consts/consts');
const CODE = require('../app/consts/code');
const sngTournaments = require('../config/data/sngTournaments.json');

// Mock data
const mockTournament = {
    id: 1,
    code: 'SNG_TEST_001',
    status: 'IN_PROGRESS',
    player_capacity: 5,
    buy_in: 50000000,
    fee: 5000000,
    reward_pool: 250000000, // 5 players * 50M buy_in
    created_at: new Date(),
    started_at: new Date(),
    ended_at: null
};

const mockPlayer = {
    id: 101,
    nick_name: 'TestPlayer1',
    display_name: 'Test Player 1',
    avatar: 'avatar1.png',
    balance: 1000000000
};

// Mock message service
const mockMessageService = {
    pushMessageByUid: async function(uid, route, data, code) {
        console.log(`✓ Message sent to player ${uid}:`);
        console.log(`  Route: ${route}`);
        console.log(`  Code: ${code}`);
        console.log(`  Tournament ID: ${data.tournament?.id}`);
        console.log(`  Player Rank: ${data.players?.[0]?.rank}`);
        console.log(`  Reward Amount: ${data.rewards?.[0]?.reward_amount?.toLocaleString()} chips`);
        return Promise.resolve();
    }
};

// Test class with our ranking logic
class SngRankingTester {
    constructor() {
        this.gameMode = consts.GAME.MODE.SNG;
        this.instance = {
            tournament_id: mockTournament.id
        };
        
        // Mock global objects
        global.messageService = mockMessageService;
    }

    /**
     * Tính toán số tiền thưởng dựa trên thứ hạng
     */
    calculateRewardAmount(tournament, rank) {
        if (!tournament || !tournament.reward_pool || rank > 3) {
            return 0;
        }

        var rewardDistribution = sngTournaments.reward_distribution;
        var percentage = 0;

        switch (rank) {
            case 1:
                percentage = rewardDistribution.first_place;
                break;
            case 2:
                percentage = rewardDistribution.second_place;
                break;
            case 3:
                percentage = rewardDistribution.third_place;
                break;
            default:
                percentage = 0;
        }

        return Math.floor((tournament.reward_pool * percentage) / 100);
    }

    /**
     * Gửi thông báo kết quả giải đấu cho người chơi
     */
    async sendTournamentResult(uid, tournament, rank, rewardAmount, playerInfo) {
        try {
            var sngCmdPushResult = consts.GAME.ROUTER.SNG_TOURNAMENT_RESULT;
            var sngResultData = {
                tournament: {
                    id: tournament.id,
                    tournament_id: tournament.id,
                    code: tournament.code,
                    status: tournament.status,
                    player_capacity: tournament.player_capacity,
                    buy_in: tournament.buy_in,
                    fee: tournament.fee,
                    reward_pool: tournament.reward_pool,
                    created_at: tournament.created_at,
                    started_at: tournament.started_at,
                    ended_at: tournament.ended_at
                },
                players: [{
                    id: uid,
                    player_id: uid,
                    player_name: playerInfo.nick_name || playerInfo.display_name || 'Player',
                    seat_number: 0,
                    initial_chips: 100000000,
                    current_chips: 0,
                    status: "ELIMINATED",
                    eliminated_at_hand: 0,
                    rank: rank,
                    avatar: playerInfo.avatar || ''
                }],
                rewards: [{
                    tournament_id: tournament.id,
                    player_id: uid,
                    rank: rank,
                    reward_amount: rewardAmount,
                    player_name: playerInfo.nick_name || playerInfo.display_name || 'Player',
                    avatar: playerInfo.avatar || ''
                }]
            };
            
            var sngCode = CODE.SNG.TOURNAMENT_RESULT;
            await mockMessageService.pushMessageByUid(uid, sngCmdPushResult, sngResultData, sngCode);
            
            console.log(`✓ Tournament result notification sent successfully`);
            
        } catch (error) {
            console.error("❌ Error sending tournament result:", error);
        }
    }

    /**
     * Kiểm tra thứ hạng và gửi thông báo (simplified version for testing)
     */
    async testRankingLogic(uid, rank, playerInfo) {
        console.log(`\n--- Testing rank ${rank} for player ${uid} ---`);
        
        // Chỉ gửi thông báo cho top 3
        if (rank <= 3) {
            // Tính toán phần thưởng
            var rewardAmount = this.calculateRewardAmount(mockTournament, rank);
            
            console.log(`✓ Player achieved top 3:`);
            console.log(`  Player ID: ${uid}`);
            console.log(`  Rank: ${rank}`);
            console.log(`  Reward Amount: ${rewardAmount.toLocaleString()} chips`);

            // Gửi thông báo SNG_TOURNAMENT_RESULT
            await this.sendTournamentResult(uid, mockTournament, rank, rewardAmount, playerInfo);
            
            return { rank, rewardAmount, notified: true };
        } else {
            console.log(`✓ Player rank ${rank} - No notification sent (not in top 3)`);
            return { rank, rewardAmount: 0, notified: false };
        }
    }
}

// Test functions
async function testRewardCalculation() {
    console.log('=== Testing Reward Calculation ===');
    
    const tester = new SngRankingTester();
    
    // Test reward calculation for different ranks
    const rank1Reward = tester.calculateRewardAmount(mockTournament, 1);
    const rank2Reward = tester.calculateRewardAmount(mockTournament, 2);
    const rank3Reward = tester.calculateRewardAmount(mockTournament, 3);
    const rank4Reward = tester.calculateRewardAmount(mockTournament, 4);
    
    console.log('Reward calculations:');
    console.log(`- Rank 1 (50%): ${rank1Reward.toLocaleString()} chips`);
    console.log(`- Rank 2 (30%): ${rank2Reward.toLocaleString()} chips`);
    console.log(`- Rank 3 (20%): ${rank3Reward.toLocaleString()} chips`);
    console.log(`- Rank 4 (0%): ${rank4Reward.toLocaleString()} chips`);
    
    // Verify calculations
    const expectedRank1 = Math.floor(mockTournament.reward_pool * 0.5);
    const expectedRank2 = Math.floor(mockTournament.reward_pool * 0.3);
    const expectedRank3 = Math.floor(mockTournament.reward_pool * 0.2);
    
    console.log('\nVerification:');
    console.log(`✓ Rank 1: ${rank1Reward === expectedRank1 ? 'PASS' : 'FAIL'} (Expected: ${expectedRank1.toLocaleString()})`);
    console.log(`✓ Rank 2: ${rank2Reward === expectedRank2 ? 'PASS' : 'FAIL'} (Expected: ${expectedRank2.toLocaleString()})`);
    console.log(`✓ Rank 3: ${rank3Reward === expectedRank3 ? 'PASS' : 'FAIL'} (Expected: ${expectedRank3.toLocaleString()})`);
    console.log(`✓ Rank 4: ${rank4Reward === 0 ? 'PASS' : 'FAIL'} (Expected: 0)`);
    
    return {
        rank1: rank1Reward === expectedRank1,
        rank2: rank2Reward === expectedRank2,
        rank3: rank3Reward === expectedRank3,
        rank4: rank4Reward === 0
    };
}

async function testRankingNotifications() {
    console.log('\n=== Testing Ranking Notifications ===');
    
    const tester = new SngRankingTester();
    const results = [];
    
    // Test different ranks
    for (let rank = 1; rank <= 5; rank++) {
        const player = {
            ...mockPlayer,
            id: 100 + rank,
            nick_name: `TestPlayer${rank}`
        };
        
        const result = await tester.testRankingLogic(player.id, rank, player);
        results.push(result);
    }
    
    console.log('\n--- Summary ---');
    results.forEach((result, index) => {
        const rank = index + 1;
        console.log(`Rank ${rank}: ${result.notified ? '✓ NOTIFIED' : '✗ NOT NOTIFIED'} (Reward: ${result.rewardAmount.toLocaleString()})`);
    });
    
    // Verify that only top 3 got notifications
    const top3Notified = results.slice(0, 3).every(r => r.notified);
    const bottom2NotNotified = results.slice(3).every(r => !r.notified);
    
    console.log(`\n✓ Top 3 notified: ${top3Notified ? 'PASS' : 'FAIL'}`);
    console.log(`✓ Bottom 2 not notified: ${bottom2NotNotified ? 'PASS' : 'FAIL'}`);
    
    return { top3Notified, bottom2NotNotified };
}

async function testMessageFormat() {
    console.log('\n=== Testing Message Format ===');
    
    const tester = new SngRankingTester();
    
    // Test message format for rank 1
    console.log('\nTesting message format for rank 1:');
    await tester.testRankingLogic(101, 1, mockPlayer);
    
    return true;
}

// Main test runner
async function runTests() {
    console.log('🚀 Starting SNG Ranking Logic Test');
    console.log('=====================================');
    
    try {
        // Test reward calculation
        const rewardTest = await testRewardCalculation();
        
        // Test ranking notifications
        const notificationTest = await testRankingNotifications();
        
        // Test message format
        const messageTest = await testMessageFormat();
        
        // Summary
        console.log('\n🎯 Test Results Summary');
        console.log('========================');
        
        const allRewardTests = Object.values(rewardTest).every(Boolean);
        const allNotificationTests = Object.values(notificationTest).every(Boolean);
        
        console.log(`✓ Reward Calculation: ${allRewardTests ? 'PASS' : 'FAIL'}`);
        console.log(`✓ Ranking Notifications: ${allNotificationTests ? 'PASS' : 'FAIL'}`);
        console.log(`✓ Message Format: ${messageTest ? 'PASS' : 'FAIL'}`);
        
        const allTestsPassed = allRewardTests && allNotificationTests && messageTest;
        
        if (allTestsPassed) {
            console.log('\n🎉 All tests PASSED! The ranking logic is working correctly.');
        } else {
            console.log('\n❌ Some tests FAILED. Please check the implementation.');
        }
        
        return allTestsPassed;
        
    } catch (error) {
        console.error('\n❌ Test failed with error:', error);
        return false;
    }
}

// Run the test
if (require.main === module) {
    runTests().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { runTests };
