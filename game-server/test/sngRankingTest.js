/**
 * Test script for SNG Tournament Ranking and Result Notification functionality
 * 
 * This script tests the new ranking check and tournament result notification features
 */

const { models } = require('../app/models');
const sngConsts = require('../app/consts/sngConsts');
const consts = require('../app/consts/consts');
const CODE = require('../app/consts/code');
const sngTournaments = require('../config/data/sngTournaments.json');
const moment = require('moment');

// Mock pomelo app for testing
const mockPomelo = {
    app: {
        rpc: {
            db: {
                dbRemote: {
                    getSngTournamentById: function(serverId, tournamentId, callback) {
                        console.log('[Mock] getSngTournamentById called with:', tournamentId);
                        // Simulate database call
                        models.SngTournament.findByPk(tournamentId)
                            .then(tournament => {
                                if (tournament) {
                                    callback(null, CODE.OK, tournament);
                                } else {
                                    callback(null, CODE.NOT_FOUND, null);
                                }
                            })
                            .catch(err => callback(err, CODE.FAIL, null));
                    },
                    countActiveSngTournamentPlayers: function(serverId, tournamentId, callback) {
                        console.log('[Mock] countActiveSngTournamentPlayers called with:', tournamentId);
                        // Simulate database call
                        models.SngTournamentPlayer.count({
                            where: {
                                tournament_id: tournamentId,
                                status: 'ACTIVE'
                            }
                        })
                        .then(count => callback(null, CODE.OK, count))
                        .catch(err => callback(err, CODE.FAIL, 0));
                    },
                    updatePlayerBalance: function(serverId, playerId, amount, description, callback) {
                        console.log('[Mock] updatePlayerBalance called with:', playerId, amount, description);
                        // Simulate balance update
                        models.Player.findByPk(playerId)
                            .then(player => {
                                if (player) {
                                    const oldBalance = player.balance;
                                    player.balance += amount;
                                    return player.save().then(() => {
                                        callback(null, CODE.OK, {
                                            old_balance: oldBalance,
                                            new_balance: player.balance,
                                            balance: player.balance
                                        });
                                    });
                                } else {
                                    callback(null, CODE.NOT_FOUND, null);
                                }
                            })
                            .catch(err => callback(err, CODE.FAIL, null));
                    },
                    getPlayerById: function(serverId, playerId, callback) {
                        console.log('[Mock] getPlayerById called with:', playerId);
                        // Simulate database call
                        models.Player.findByPk(playerId)
                            .then(player => {
                                if (player) {
                                    callback(null, CODE.OK, player);
                                } else {
                                    callback(null, CODE.NOT_FOUND, null);
                                }
                            })
                            .catch(err => callback(err, CODE.FAIL, null));
                    }
                }
            }
        }
    }
};

// Mock message service
const mockMessageService = {
    pushMessageByUid: async function(uid, route, data, code) {
        console.log('[Mock] Message sent to player:', uid);
        console.log('[Mock] Route:', route);
        console.log('[Mock] Code:', code);
        console.log('[Mock] Data:', JSON.stringify(data, null, 2));
        return Promise.resolve();
    }
};

// Mock SNG Table class with our new methods
class MockSngTable {
    constructor(tournamentId) {
        this.gameMode = consts.GAME.MODE.SNG;
        this.instance = {
            tournament_id: tournamentId
        };
        
        // Inject mock pomelo
        global.pomelo = mockPomelo;
        global.messageService = mockMessageService;
    }

    /**
     * Tính toán số tiền thưởng dựa trên thứ hạng
     */
    calculateRewardAmount(tournament, rank) {
        if (!tournament || !tournament.reward_pool || rank > 3) {
            return 0;
        }

        var rewardDistribution = sngTournaments.reward_distribution;
        var percentage = 0;

        switch (rank) {
            case 1:
                percentage = rewardDistribution.first_place;
                break;
            case 2:
                percentage = rewardDistribution.second_place;
                break;
            case 3:
                percentage = rewardDistribution.third_place;
                break;
            default:
                percentage = 0;
        }

        return Math.floor((tournament.reward_pool * percentage) / 100);
    }

    /**
     * Gửi thông báo kết quả giải đấu cho người chơi
     */
    async sendTournamentResult(uid, tournament, rank, rewardAmount, playerInfo) {
        try {
            var sngCmdPushResult = consts.GAME.ROUTER.SNG_TOURNAMENT_RESULT;
            var sngResultData = {
                tournament: {
                    id: tournament.id,
                    tournament_id: tournament.id,
                    code: tournament.code,
                    status: tournament.status,
                    player_capacity: tournament.player_capacity,
                    buy_in: tournament.buy_in,
                    fee: tournament.fee,
                    reward_pool: tournament.reward_pool,
                    created_at: tournament.created_at,
                    started_at: tournament.started_at,
                    ended_at: tournament.ended_at
                },
                players: [{
                    id: uid,
                    player_id: uid,
                    player_name: playerInfo.nick_name || playerInfo.display_name || 'Player',
                    seat_number: 0,
                    initial_chips: 100000000,
                    current_chips: 0,
                    status: "ELIMINATED",
                    eliminated_at_hand: 0,
                    rank: rank,
                    avatar: playerInfo.avatar || ''
                }],
                rewards: [{
                    tournament_id: tournament.id,
                    player_id: uid,
                    rank: rank,
                    reward_amount: rewardAmount,
                    player_name: playerInfo.nick_name || playerInfo.display_name || 'Player',
                    avatar: playerInfo.avatar || ''
                }]
            };
            
            var sngCode = CODE.SNG.TOURNAMENT_RESULT;
            await mockMessageService.pushMessageByUid(uid, sngCmdPushResult, sngResultData, sngCode);
            
            console.log("[sendTournamentResult] Tournament result notification sent to player:", {
                player_id: uid,
                rank: rank,
                reward_amount: rewardAmount,
                tournament_id: tournament.id
            });
            
        } catch (error) {
            console.error("[sendTournamentResult] Error sending tournament result:", error);
        }
    }

    /**
     * Cộng chip thưởng cho người chơi
     */
    async awardPlayerPrize(uid, rewardAmount, rank) {
        try {
            // Cập nhật balance của người chơi
            mockPomelo.app.rpc.db.dbRemote.updatePlayerBalance('*', uid, rewardAmount, 
                'SNG Tournament Prize - Position ' + rank, function(updateErr, updateCode, updateResult) {
                if (!updateErr && updateResult) {
                    console.log("[awardPlayerPrize] Prize awarded successfully:", {
                        player_id: uid,
                        rank: rank,
                        reward_amount: rewardAmount,
                        new_balance: updateResult.balance
                    });

                    // Gửi thông báo cập nhật balance
                    var _cmdPush = consts.GAME.ROUTER.UPDATE_MYSELF;
                    var _arrMsg = {
                        type: CODE.USER.BALANCE,
                        msg: "SNG Tournament Prize Awarded",
                        balance: updateResult.balance,
                        prize_amount: rewardAmount,
                        rank: rank
                    };
                    var _code = CODE.USER.UPDATE_MONEY;
                    mockMessageService.pushMessageByUid(uid, _cmdPush, _arrMsg, _code);
                    
                } else {
                    console.error("[awardPlayerPrize] Error updating player balance:", updateErr);
                }
            });
            
        } catch (error) {
            console.error("[awardPlayerPrize] Error awarding prize:", error);
        }
    }

    /**
     * Kiểm tra thứ hạng của người chơi và gửi thông báo kết quả giải đấu nếu đạt top 3
     */
    checkPlayerRankAndSendResult(uid, playerName, seatNr, playerInfo) {
        var me = this;
        
        // Chỉ xử lý cho SNG tournament
        if (this.gameMode !== consts.GAME.MODE.SNG || !this.instance.tournament_id) {
            return;
        }

        console.log("[sngTable.checkPlayerRankAndSendResult] Checking rank for player:", uid, "tournament:", this.instance.tournament_id);

        // Lấy thông tin tournament từ database
        mockPomelo.app.rpc.db.dbRemote.getSngTournamentById('*', this.instance.tournament_id, async function(e, code, tournament) {
            if (e || !tournament) {
                console.error("[checkPlayerRankAndSendResult] Error getting tournament:", e);
                return;
            }

            // Đếm số người chơi còn lại (ACTIVE)
            mockPomelo.app.rpc.db.dbRemote.countActiveSngTournamentPlayers('*', me.instance.tournament_id, async function(e, code, activeCount) {
                if (e) {
                    console.error("[checkPlayerRankAndSendResult] Error counting active players:", e);
                    return;
                }

                // Tính thứ hạng: số người còn lại + 1
                var playerRank = activeCount + 1;
                
                console.log("[checkPlayerRankAndSendResult] Player rank calculated:", {
                    player_id: uid,
                    rank: playerRank,
                    active_players: activeCount,
                    tournament_id: me.instance.tournament_id
                });

                // Chỉ gửi thông báo cho top 3
                if (playerRank <= 3) {
                    // Tính toán phần thưởng
                    var rewardAmount = me.calculateRewardAmount(tournament, playerRank);
                    
                    console.log("[checkPlayerRankAndSendResult] Player achieved top 3:", {
                        player_id: uid,
                        rank: playerRank,
                        reward_amount: rewardAmount
                    });

                    // Gửi thông báo SNG_TOURNAMENT_RESULT
                    await me.sendTournamentResult(uid, tournament, playerRank, rewardAmount, playerInfo);

                    // Cộng chip thưởng cho người chơi (nếu có)
                    if (rewardAmount > 0) {
                        await me.awardPlayerPrize(uid, rewardAmount, playerRank);
                    }
                }
            });
        });
    }
}

module.exports = MockSngTable;
