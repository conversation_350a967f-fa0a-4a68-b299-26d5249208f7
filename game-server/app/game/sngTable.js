var logger          = require('pomelo-logger').getLogger('game-log', __filename);
var pomelo          = require('pomelo');
var events          = require('events');
var Game            = require('./sngGame');
var Player          = require('./player');
var GAME_SETTINGS   = require('../../config/gameSettings.json');
var utils           = require('../util/utils');
var _               = require('underscore');
var messageService  = require('../services/messageService');
var consts          = require('../consts/consts');
var CODE            = require('../consts/code');
var sngConsts       = require('../consts/sngConsts');
var sngTournaments  = require('../../config/data/sngTournaments.json');
// var userDao         = require('../dao/userDao');

/**
 * Table object handles table logic while it is stored in memory.
 *
 * @param {number} smallBlind small blind
 * @param {number} bigBlind big blind
 * @param {number} minPlayers minimum number of players before game can be started
 * @param {number} maxPlayers maximum number of players before game can be started
 * @param {number} minBuyIn minimum buy in
 * @param {number} maxBuyIn maximum buy in
 * @param {string} gameMode type of game
 * @param {object} table instance of a table
 *
 */
module.exports = Table = function(smallBlind, bigBlind, minPlayers, maxPlayers, minBuyIn, maxBuyIn, gameMode, table){
    this.smallBlind = smallBlind;
    this.bigBlind = bigBlind;
    this.minPlayers = minPlayers;
    this.maxPlayers =  maxPlayers;
    this.players = [];
    this.dealer = 0; //Track the dealer position between games
    this.lastDealerUid = 0; // dealerUid default
    this.minBuyIn = minBuyIn;
    this.maxBuyIn = maxBuyIn;
    this.previousPlayers = [];
    this.playersToRemove = [];
    this.playersToAdd = [];
    this.playersToNotEnough = []; // danh sach nguoi choi khong du tien choi tiep
    this.eventEmitter = new events.EventEmitter();
    this.turnBet = {};
    this.gameWinners = [];
    this.gameLosers = [];
    this.actions = [];
    this.members = [];
    this.active = false;
    this.isShowBoard = true; // state for client show board true or false
    this.isAction = false; // trạng thái đang xử lý 1 command action từ người chơi gửi lên từng người 1, mặc định là false
    this.isSitDown = false; // trạng thái = true khi có người bắt đầu vào ngồi, sẽ set lại false khi người đó hoàn tất quá trình
    //this.isFirst = true; // trạng thái bàn đầu tiên được khởi tạo
    this.gameMode = gameMode;
    this.instance = table;
    this.currentPlayerTime = 0; // thời gian của người chơi hiện tại, 11/02/2025
    this.ante = 0; // Ante for SNG tournaments
    //Validate acceptable value ranges.
    var err;
    if(minPlayers < 2){ //require at least two players to start a game.
        err = new Error(101, 'Parameter [minPlayers] must be a postive integer of a minimum value of 2.');
    }else if(maxPlayers > 10){ //hard limit of 10 players at a table.
        err = new Error(102, 'Parameter [maxPlayers] must be a positive integer less than or equal to 10.');
    }else if(minPlayers > maxPlayers){ //Without this we can never start a game!
        err = new Error(103, 'Parameter [minPlayers] must be less than or equal to [maxPlayers].');
    }
    if(err){
        return err;
    }
};

/**
 * - Hàm khởi tạo lại danh sách lượt chơi mới
 * -> lấy danh sách những người đã thoát ra và push lại thông tin tiền của họ
 */
//Table.prototype.initNewGame = function(){
Table.prototype.initNewGame = function(stateName){
    logger.info("[table.initNewGame] stateName: ", stateName);
    var i;
    this.instance.state = stateName || 'JOIN';

    // Không tăng dealer ở đây vì chúng ta sẽ cập nhật trong NewRound
    // this.dealer += 1;
    // if(this.dealer >= this.players.length){
    //     this.dealer = 0;
    // }

    delete this.game;
    this.previousPlayers    = [];

    // Thêm người chơi hiện có và xoá những người chơi đã thoát hoặc bị out
    for(i=0;i<this.players.length;++i){
        this.previousPlayers.push(this.players[i]);

        if(this.playersToRemove.indexOf(i) === -1){
            logger.info("[initNewGame] >> addPlayer: ", this.players[i]);
            //this.AddPlayer(this.players[i].actorNr, this.players[i].playerName, this.players[i].chips, this.players[i].id);
            this.AddPlayer(this.players[i].actorNr, this.players[i].playerName, this.players[i].chips, this.players[i].id, this.players[i].avatar, this.players[i].level, this.players[i].vippoint, this.players[i].exp, this.players[i].type);
        }else{
            logger.info("[initNewGame] >> player đã thoát ra trước đó: ", this.players[i]);
            logger.info("[initNewGame] >> push noti thôi ");

            // push message cho những người đã thoát khỏi bàn để cập nhật lại số tiền
            // ---------------------------------------------------------------------------------------------------
            this.__updatePlayerRemoved(this.players[i].id);

        }
    }

    this.players            = [];
    this.playersToRemove    = [];
    this.actions            = [];
    this.eventEmitter.emit('gameInit');
};

Table.prototype.StartGame = function(){
    //If there is no current game and we have enough players, start a new game.
    this.instance.state = 'IN_PROGRESS';
    this.active = true;

    // reset queue check no action in table
    // ------------------------------------------------------------------------------------------
    /*
    var _tid = this.instance.id;
    this.instance.tableService.app.rpc.manager.userRemote.clearJobQueueActionByTableId(null, _tid, function (e, _res){
        logger.info("[After] clearJobQueueActionByTableId e ", e, " res : ", _res);
    });
    */

    if(!this.game){
        this.game = new Game(this.smallBlind, this.bigBlind);
        this.NewRound();
    }
};


Table.prototype.AddPlayer = function(seatNr, playerName, chips, uid, avatar, level, vippoint, exp, type) {
    //if(chips >= this.minBuyIn && chips <= this.maxBuyIn){
    var isState = false;

    // logger.info("[table] AddPlayer -> previousPlayers: ", this.previousPlayers);
    // const isExistingInPreviousPlayer = _.some(this.previousPlayers, player => player.id === uid);
    // logger.info("[table] AddPlayer -> isExistingInPreviousPlayer: ", isExistingInPreviousPlayer);

    // For new players: they must have at least the minimum buy-in
    // For existing players: they can continue if they have enough chips for the blinds
    // const minimumToPlay = Math.max(this.smallBlind, this.bigBlind);

    logger.info("[sngTable.AddPlayer] -> Player " + playerName + " (ID: " + uid + ") allowed to sit. " +
                "Chips: " + chips + ", MinBuyIn: " + this.minBuyIn +
                // ", IsExisting: " + isExistingInPreviousPlayer +
                // ", MinimumToPlay: " + minimumToPlay +
                ", smallBlind: " + this.smallBlind +
                ", bigBlind: " + this.bigBlind);

    // if(chips >= this.minBuyIn || (isExistingInPreviousPlayer && chips > 0 && chips >= minimumToPlay)) {
    // với giải đấu SNG thì chỉ cần người chơi còn tiền thì còn được chơi đến khi nào hết tiền, vì họ đã trả phí buy-in duy nhất 1 lần khi đăng ký tham gia.
    if(chips > 0) {
        //var player = new Player(seatNr, playerName, chips, uid, this);
        var player      = new Player(seatNr, playerName, chips, uid, avatar, level, vippoint, exp, type, this);
        player.isState  = true; // set la ngồi
        player.oldChips = chips; // set lai so tien cua van truoc
        // - check xem player da co trong danh sachs hay chua
        for(var i = 0; i<this.playersToAdd.length; i+=1) {
            if (this.playersToAdd[i].id === uid) {
                //this.playersToAdd.push(player);
                isState = true;
            }
        }
        if (!isState) {
            this.playersToAdd.push(player);
        }

    } else {
        var me = this;
        logger.info("AddPlayer -> Thông tin người chơi không đủ tiền: ", playerName, "(", uid, "), chips: ", chips, ' -> minBuyIn: ', this.minBuyIn);

        // Đẩy những người chơi ko đủ tiền vào array để thông báo cho họ biết: playersToNotEnoughChips
        // ----------------------------------------------------------------------------------------------------------
        var player = new Player(seatNr, playerName, chips, uid, avatar, level, vippoint, exp, type, this);
        player.isState = false; // set la dung
        this.playersToNotEnough.push(player);

        // set lại gameId = null cho những user này
        pomelo.app.rpc.manager.userRemote.disGameId(null, uid, function (_, res) {
            logger.info("disGameId uid ", uid, " res: ", res);
            //me.eventEmitter.emit('playerNotEnoughChips');
        });

        // Push message to uid
        // ----------------------------------------------------------------------------------------------------------
        // userDao.getPlayerByUid(uid, function (e, res) {
        // Chỉ cần lấy thông tin user và tiền từ service coin grpc là được
        // pomelo.app.rpc.coin.coinRemote.getAccountInfo('*', {userId: uid}, async (e, code, res) => {
        pomelo.app.rpc.db.dbRemote.getPlayerById('*', uid, async (e, code, res) => {
            logger.info("[table.AddPlayer] getPlayerById >> e: ", e, ' -> code: ', code, ' -> user: ', res);
            if (res != null) {
                // if (!err) {
                // - Check số tiền hiện tại < 0 thì cập nhật lại về 0 và return về cho client
                // --------------------------------------------------------------------------------------------------
                logger.info("Kiem tra số tiền hiện tại < 0 hay không , nếu có thì cập nhật về 0 ", res);
                me.instance.tableService.checkAndUpdateDataPlayer(res);

                // - Push command to user
                // ----------------------------------------------------------------------------------------------------------
                var _cmdPush    = consts.GAME.ROUTER.UPDATE_MYSELF;
                var _msgPush    = "Update Money";
                var _arrMsg     = {
                    type: CODE.USER.BALANCE,
                    msg: _msgPush,
                    balance: res?.balance ?? 0
                };
                var _code       = CODE.USER.UPDATE_MONEY;
                await messageService.pushMessageByUid(uid, _cmdPush, _arrMsg, _code);

                // Push message SNG_TOURNAMENT_PLAYER_ELIMINATED (thông báo người chơi bị loại) to player
                // ----------------------------------------------------------------------------------------------------------
                var sngCmdPushEliminated    = consts.GAME.ROUTER.SNG_TOURNAMENT_PLAYER_ELIMINATED;
                // var _msgPush    = "Bạn đã bị loại khỏi giải đấu vì hết tiền";
                var sngMsgData     = {
                    tournament_id: me.instance.tournament_id,
                    player: {
                        id: res.id,
                        player_id: res.id,
                        player_name: res.nick_name,
                        seat_number: seatNr,
                        initial_chips: chips,
                        current_chips: chips,
                        status: "ELIMINATED",
                        eliminated_at_hand: 0,
                        rank: 0,
                        avatar: res.avatar
                    },
                    remaining_players: 0,
                    total_players: 0,
                    current_blind_level: 0,
                    small_blind: 0,
                    big_blind: 0,
                    ante: 0
                };
                var sngCode       = CODE.SNG.PLAYER_ELIMINATED;
                await messageService.pushMessageByUid(uid, sngCmdPushEliminated, sngMsgData, sngCode);

                // Kiểm tra thứ hạng và gửi thông báo kết quả giải đấu (nếu đạt top 3)
                // ----------------------------------------------------------------------------------------------------------
                me.checkPlayerRankAndSendResult(uid, res.nick_name, seatNr, res);

            } // end check res != null
        });

        this.eventEmitter.emit('playerNotEnoughChips');
    } // end if
};

/**
 * Hàm move người chơi từ đang ngồi chờ => đứng dậy chầu rìa
 * @param pid
 */
Table.prototype.standUpPlayer = function (pid) {
    for(var i in this.players ) {
        // set trang thai la dung
        this.players[i].isState = false;

        if(this.players[i].id === pid) {
            this.playersToRemove.push( parseInt(i) );
            //this.players[i].Fold();
        }
    }
    for(var i in this.playersToAdd ) {
        if(this.playersToAdd[i].id === pid) {
            this.playersToAdd.splice(i, 1);
        }
    }

    // nhóm ko đủ tiền chơi tiếp
    for(var i in this.playersToNotEnough ) {
        if(this.playersToNotEnough[i].id === pid){
            this.playersToNotEnough.splice(i, 1);
        }
    }

    /*
    for(var i in this.members ){
        if(this.members[i].id === pid){
            this.members.splice(i, 1);
        }
    }
    */

    for(var i in this.previousPlayers) {
        if(this.previousPlayers[i].id === pid) {
            this.previousPlayers.splice(i, 1);
        }
    }
    logger.info("standUpPlayer >> playersToRemove: ", this.playersToRemove);
    this.eventEmitter.emit("playerLeftGame");
};

Table.prototype.removePlayer = function(pid) {
    for(var i in this.players ) {
        if(this.players[i].id === pid){
            this.playersToRemove.push( parseInt(i) );
            //this.players[i].Fold();
        }
    }
    for(var i in this.playersToAdd ){
        if(this.playersToAdd[i].id === pid){
            this.playersToAdd.splice(i, 1);
        }
    }
    // nhóm ko đủ tiền chơi tiếp
    for(var i in this.playersToNotEnough ){
        if(this.playersToNotEnough[i].id === pid){
            this.playersToNotEnough.splice(i, 1);
        }
    }
    for(var i in this.members ){
        if(this.members[i].id === pid){
            this.members.splice(i, 1);
        }
    }

    for(var i in this.previousPlayers){
        if(this.previousPlayers[i].id === pid){
            this.previousPlayers.splice(i, 1);
        }
    }
    //console.log("removePlayer: ", this.playersToRemove);
    this.eventEmitter.emit("playerLeft");
};

Table.prototype.NewRound = function(){
    console.log("[app||game||table => NewRound -----------------------------");

    // Increment hand number for SNG tournaments
    if (this.game && this.gameMode === consts.GAME.MODE.SNG) {
        this.game.handNumber++;
        logger.info("[table.NewRound] -> handNumber: ", this.game.handNumber);
    }

    // Check for players who don't have enough chips to continue playing
    const minimumToPlay = Math.max(this.smallBlind, this.bigBlind);

    for (var i = 0; i < this.players.length; i++) {
        // Players need at least enough chips for the blinds to continue
        if (this.players[i].chips <= 0 || this.players[i].chips < minimumToPlay) {
            logger.info("Player " + this.players[i].playerName + " (ID: " + this.players[i].id +
                       ") doesn't have enough chips to play (" + this.players[i].chips +
                       " < " + minimumToPlay + "). Adding to playersToRemove.");

            // Add to playersToRemove
            this.playersToRemove.push(i);

            // Add to playersToNotEnough for notification
            var player = this.players[i];
            player.isState = false; // set to standing
            this.playersToNotEnough.push(player);

            // Set gameId to null for this user
            pomelo.app.rpc.manager.userRemote.disGameId(null, player.id, function (_, res) {
                logger.info("disGameId uid ", player.id, " res: ", res);
            });
        }
    }

    // Remove players who don't have enough chips
    this.playersToRemove = _.uniq(this.playersToRemove);

    // Actually remove the players from the table
    if (this.playersToRemove.length > 0) {
        // Sort in descending order to avoid index shifting issues when removing
        this.playersToRemove.sort(function(a, b) { return b - a; });

        for (var i = 0; i < this.playersToRemove.length; i++) {
            var index = this.playersToRemove[i];
            if (index >= 0 && index < this.players.length) {
                var removedPlayer = this.players[index];
                logger.info("Removing player " + removedPlayer.playerName + " (ID: " +
                           removedPlayer.id + ") from table");

                // Kiểm tra thứ hạng và gửi thông báo kết quả giải đấu (nếu đạt top 3) cho SNG tournament
                if (this.gameMode === consts.GAME.MODE.SNG && this.instance.tournament_id) {
                    var me = this;
                    // Lấy thông tin player từ database để gửi thông báo
                    pomelo.app.rpc.db.dbRemote.getPlayerById('*', removedPlayer.id, function(e, code, playerInfo) {
                        if (!e && playerInfo) {
                            me.checkPlayerRankAndSendResult(removedPlayer.id, removedPlayer.playerName, removedPlayer.seatNr, playerInfo);
                        }
                    });
                }

                this.players.splice(index, 1);
            }
        }

        // Clear the playersToRemove array
        this.playersToRemove = [];
    }

    // Add new players
    this.playersToAdd = _.uniq(this.playersToAdd);
    for(var i in this.playersToAdd) {
        this.players.push(this.playersToAdd[i]);
    }

    // Check if we have enough players to start a new round
    if (this.players.length < this.minPlayers) {
        logger.info("Not enough players to start a new round. Need at least " +
                   this.minPlayers + " players, but only have " + this.players.length);
        this.eventEmitter.emit("notEnoughPlayers");
        return; // Exit the function early
    }

    // Ván đầu tiên, lastDealerUid = 0
    if (this.lastDealerUid == 0) {
        this.lastDealerUid = 0;
    }

    // Sắp xếp lại người chơi dựa vào vị trí dealer
    this.players = utils.turnRound(this.players, this.dealer, this.lastDealerUid);
    this.players = _.uniq(this.players);

    // Cập nhật dealer sau khi sắp xếp
    // Trong mảng đã xoay, dealer luôn ở vị trí 0
    this.dealer = 0;
    this.lastDealerUid = this.players[0].id;

    this.playersToRemove = [];
    this.playersToAdd = [];
    this.gameWinners = [];
    this.gameLosers = [];
    this.playersToNotEnough = [];

    // Reset sidepots for new round
    if (this.game && this.game.sidepots) {
        this.game.sidepots = [];
    }

    var i, smallBlind, bigBlind;
    //Deal 2 cards to each player
    for(i=0;i< this.players.length;i+=1) {
        this.players[i].cards.push(this.game.deck.pop());
        this.players[i].cards.push(this.game.deck.pop());
        this.game.bets[i] = 0;
        this.game.roundBets[i] = 0;
    }

    // Xác định vị trí SB và BB theo luật poker
    if (this.players.length == 2) {
        // Trường hợp đặc biệt: 2 người chơi
        // Trong bàn 2 người, dealer cũng là SB, người còn lại là BB
        smallBlind = 0; // dealer
        bigBlind = 1;
    } else {
        // Trường hợp thông thường: 3+ người chơi
        smallBlind = 1; // Người bên trái dealer
        bigBlind = 2;   // Người bên trái SB

        // Kiểm tra nếu không đủ người chơi
        if (bigBlind >= this.players.length) {
            bigBlind = 0;
        }
    }

    // Xác định người chơi đầu tiên hành động sau khi đặt blind
    this.currentPlayer = (bigBlind + 1) % this.players.length;
    this.startIndex = this.currentPlayer;

    // Đặt tiền cược bắt buộc cho SB và BB
    this.players[smallBlind].chips -= this.smallBlind;
    this.players[bigBlind].chips -= this.bigBlind;
    this.game.bets[smallBlind] = this.smallBlind;
    this.game.bets[bigBlind] = this.bigBlind;

    // Xử lý ante cho giải đấu SNG nếu có
    if (this.ante > 0 && this.gameMode === consts.GAME.MODE.SNG) {
        for (i = 0; i < this.players.length; i++) {
            this.players[i].chips -= this.ante;
            this.game.bets[i] += this.ante;
        }
    }

    // Lưu vị trí SB và BB
    this.game.blinds = [smallBlind, bigBlind];

    // Debug
    console.log("Dealer position:", this.dealer);
    console.log("SB position:", this.game.blinds[0]);
    console.log("BB position:", this.game.blinds[1]);
    console.log("Players:", this.players.map(p => p.id));

    this.eventEmitter.emit("newRound");
};

Table.prototype.startTimer = function(){
    var me = this;
    me.stopTimer();
    me.currentPlayerTime = Math.floor(new Date().getTime() / 1000);
    me._countdown = setTimeout(function(){
        if(!me.active){
            return;
        }

        // call rpc check no action
        // ------------------------------------------------------------------------------------------
        /*
        var _tid, _uid;
        _tid = me.instance.id;
        _uid = me.players[me.currentPlayer].id;
        logger.info("startTimer >> tid: ", _tid, " => uid ", _uid);
        me.instance.tableService.app.rpc.manager.userRemote.noActionCheck(null, _tid, _uid, function (e, _res) {
            logger.info("callback where add noActionCheck: ", _res);
        });
        */

        logger.info('timer ended. executing move.');
        if(me.game.bets[me.currentPlayer] < getMaxBet(me.game.bets)){
            console.log('-> auto fold');
            me.players[me.currentPlayer].Fold();
        }else{
            var checkPlayerRemove = checkCurrentPlayerToRemove(me.players[me.currentPlayer], me.playersToRemove, me.players);
            logger.info("checkPlayerRemove: ", checkPlayerRemove);
            if (checkPlayerRemove){
                console.log('-> auto call nhung current play removed => fold');
                me.players[me.currentPlayer].Fold();
            }else{
                console.log('-> auto call');
                me.players[me.currentPlayer].Call();
            }
            //me.players[me.currentPlayer].Call();
        }
        me.instance.tableService.handleGameState(me.instance.id, function(e){
            if(e){
                console.error(e);
            }
        });
    }, (GAME_SETTINGS.gameMode[this.gameMode].timeout * 1000));
};

Table.prototype.stopTimer = function(){
    if(this._countdown){
        clearTimeout(this._countdown);
    }
};


Table.prototype.__updatePlayerRemoved = function (uid) {

    var me = this;

    logger.info("[table.__updatePlayerRemoved] with uid ", uid);

    // Push message to uid
    // ----------------------------------------------------------------------------------------------------------
    // userDao.getPlayerByUid(uid, function (e, res) {
    // chỉ cần call đến service coin GRPC để lấy thông tin user và tiền là đc
    // pomelo.app.rpc.coin.coinRemote.getAccountInfo('*', {userId: uid}, async (e, code, res) => {
    pomelo.app.rpc.db.dbRemote.getPlayerById('*', uid, async (e, code, res) => {
        logger.info("[table.__updatePlayerRemoved] >> getPlayerById >> e: ", e, ' -> code: ', code, ' -> user: ', res);

        if (res != null) {
            // Check số tiền hiện tại < 0 thì cập nhật lại về 0 và return về cho client
            // --------------------------------------------------------------------------------------------------
            logger.info("[table.__updatePlayerRemoved] >> Kiem tra số tiền hiện tại < 0 hay không , nếu có thì cập nhật về 0 ", res);
            me.instance.tableService.checkAndUpdateDataPlayer(res);

            // Push command to user
            // --------------------------------------------------------------------------------------------------
            // var _cmdPush    = consts.GAME.ROUTER.UPDATE_MYSELF;
            // var _msgPush    = "Update Money";
            // var _arrMsg     = {
            //     type: CODE.USER.BALANCE,
            //     msg: _msgPush,
            //     balance: res.balance
            // };
            // var _code       = CODE.USER.UPDATE_MONEY;
            // messageService.pushMessageByUid(uid, _cmdPush, _arrMsg, _code);
            // Step 5: Push message to uid
            // ----------------------------------------------------------------------------------------------------------
            var _cmdPush = consts.GAME.ROUTER.UPDATE_MYSELF;
            var _msgPush = "Update Money When End Game";
            var _arrMsg = {
                type: CODE.USER.UPDATE_MONEY_WHEN_END_GAME,
                msg: _msgPush,
                balance: res?.balance ?? 0
            };
            var _code = CODE.USER.UPDATE_MONEY;
            await messageService.pushMessageByUid(uid, _cmdPush, _arrMsg, _code);

        }
    });

};


function checkCurrentPlayerToRemove(player, arrRemove, players) {
    /*
    logger.info("checkCurrentPlayerToRemove >> player: ", player);
    logger.info("checkCurrentPlayerToRemove >> arrRemove: ", arrRemove);
    logger.info("checkCurrentPlayerToRemove >> players: ", players);
    */
    for(var i=0;i<arrRemove.length;i+=1){
        if (players[arrRemove[i]].id === player.id){
            return true;
        }
    }
    return false;
}

function getMaxBet(bets){
    var maxBet, i;
    maxBet = 0;
    for(i=0;i< bets.length;i+=1){
        if(bets[i] > maxBet){
            maxBet = bets[i];
        }
    }
    return maxBet;
}

/**
 * Kiểm tra thứ hạng của người chơi và gửi thông báo kết quả giải đấu nếu đạt top 3
 * @param {Object} table - Table instance
 * @param {number} uid - Player ID
 * @param {string} playerName - Player name
 * @param {number} seatNr - Seat number
 * @param {Object} playerInfo - Player information from database
 */
Table.prototype.checkPlayerRankAndSendResult = function(uid, playerName, seatNr, playerInfo) {
    logger.info("[sngTable.checkPlayerRankAndSendResult] >> uid: ", uid, " -> playerName: ", playerName, " -> seatNr: ", seatNr, " -> playerInfo: ", playerInfo);
    var me = this;

    // Chỉ xử lý cho SNG tournament
    if (this.gameMode !== consts.GAME.MODE.SNG || !this.instance.tournament_id) {
        return;
    }

    logger.info("[sngTable.checkPlayerRankAndSendResult] Checking rank for player:", uid, "tournament:", this.instance.tournament_id);

    // Lấy thông tin tournament từ database
    pomelo.app.rpc.db.dbRemote.getSngTournamentById('*', this.instance.tournament_id, async function(e, code, tournament) {
        if (e || !tournament) {
            logger.error("[checkPlayerRankAndSendResult] Error getting tournament:", e);
            return;
        }

        // Đếm số người chơi còn lại (ACTIVE)
        pomelo.app.rpc.db.dbRemote.countActiveSngTournamentPlayers('*', me.instance.tournament_id, async function(e, code, activeCount) {
            if (e) {
                logger.error("[checkPlayerRankAndSendResult] Error counting active players:", e);
                return;
            }

            // Tính thứ hạng: số người còn lại + 1
            var playerRank = activeCount + 1;

            logger.info("[checkPlayerRankAndSendResult] Player rank calculated:", {
                player_id: uid,
                rank: playerRank,
                active_players: activeCount,
                tournament_id: me.instance.tournament_id
            });

            // Chỉ gửi thông báo cho top 3
            if (playerRank <= 3) {
                // Tính toán phần thưởng
                var rewardAmount = me.calculateRewardAmount(tournament, playerRank);

                logger.info("[checkPlayerRankAndSendResult] Player achieved top 3:", {
                    player_id: uid,
                    rank: playerRank,
                    reward_amount: rewardAmount
                });

                // Gửi thông báo SNG_TOURNAMENT_RESULT
                await me.sendTournamentResult(uid, tournament, playerRank, rewardAmount, playerInfo);

                // Cộng chip thưởng cho người chơi (nếu có)
                if (rewardAmount > 0) {
                    await me.awardPlayerPrize(uid, rewardAmount, playerRank);
                }
            }
        });
    });
};

/**
 * Tính toán số tiền thưởng dựa trên thứ hạng
 * @param {Object} tournament - Tournament data
 * @param {number} rank - Player rank (1, 2, 3)
 * @returns {number} Reward amount
 */
Table.prototype.calculateRewardAmount = function(tournament, rank) {
    if (!tournament || !tournament.reward_pool || rank > 3) {
        return 0;
    }

    var rewardDistribution = sngTournaments.reward_distribution;
    var percentage = 0;

    switch (rank) {
        case 1:
            percentage = rewardDistribution.first_place;
            break;
        case 2:
            percentage = rewardDistribution.second_place;
            break;
        case 3:
            percentage = rewardDistribution.third_place;
            break;
        default:
            percentage = 0;
    }

    return Math.floor((tournament.reward_pool * percentage) / 100);
};

/**
 * Gửi thông báo kết quả giải đấu cho người chơi
 * @param {number} uid - Player ID
 * @param {Object} tournament - Tournament data
 * @param {number} rank - Player rank
 * @param {number} rewardAmount - Reward amount
 * @param {Object} playerInfo - Player information
 */
Table.prototype.sendTournamentResult = async function(uid, tournament, rank, rewardAmount, playerInfo) {
    try {
        var sngCmdPushResult = consts.GAME.ROUTER.SNG_TOURNAMENT_RESULT;
        var sngResultData = {
            tournament: {
                id: tournament.id,
                tournament_id: tournament.id,
                code: tournament.code,
                status: tournament.status,
                player_capacity: tournament.player_capacity,
                buy_in: tournament.buy_in,
                fee: tournament.fee,
                reward_pool: tournament.reward_pool,
                created_at: tournament.created_at,
                started_at: tournament.started_at,
                ended_at: tournament.ended_at
            },
            players: [{
                id: uid,
                player_id: uid,
                player_name: playerInfo.nick_name || playerInfo.display_name || 'Player',
                seat_number: 0, // Will be updated if needed
                initial_chips: 100000000, // Default SNG chips
                current_chips: 0, // Player is eliminated
                status: "ELIMINATED",
                eliminated_at_hand: 0,
                rank: rank,
                avatar: playerInfo.avatar || ''
            }],
            rewards: [{
                tournament_id: tournament.id,
                player_id: uid,
                rank: rank,
                reward_amount: rewardAmount,
                player_name: playerInfo.nick_name || playerInfo.display_name || 'Player',
                avatar: playerInfo.avatar || ''
            }]
        };

        var sngCode = CODE.SNG.TOURNAMENT_RESULT;
        await messageService.pushMessageByUid(uid, sngCmdPushResult, sngResultData, sngCode);

        logger.info("[sendTournamentResult] Tournament result notification sent to player:", {
            player_id: uid,
            rank: rank,
            reward_amount: rewardAmount,
            tournament_id: tournament.id
        });

    } catch (error) {
        logger.error("[sendTournamentResult] Error sending tournament result:", error);
    }
};

/**
 * Cộng chip thưởng cho người chơi
 * @param {number} uid - Player ID
 * @param {number} rewardAmount - Reward amount
 * @param {number} rank - Player rank
 */
Table.prototype.awardPlayerPrize = async function(uid, rewardAmount, rank) {
    try {
        // Cập nhật balance của người chơi trong database
        pomelo.app.rpc.db.dbRemote.updatePlayerBalance('*', uid, rewardAmount,
            'SNG Tournament Prize - Position ' + rank, function(updateErr, updateCode, updateResult) {
            if (!updateErr && updateResult) {
                logger.info("[awardPlayerPrize] Prize awarded successfully:", {
                    player_id: uid,
                    rank: rank,
                    reward_amount: rewardAmount,
                    new_balance: updateResult.balance
                });

                // Cập nhật tiền vào userCached của in-memory user
                pomelo.app.rpc.manager.userRemote.incrUserCachedBalance(null, uid, rewardAmount, function (error, user) {
                    if (!error && user) {
                        logger.info("[awardPlayerPrize] UserCached balance updated successfully:", {
                            player_id: uid,
                            cached_balance: user.balance,
                            prize_amount: rewardAmount
                        });
                    } else {
                        logger.error("[awardPlayerPrize] Error updating userCached balance:", error);
                    }
                });

                // Gửi thông báo cập nhật balance
                var _cmdPush = consts.GAME.ROUTER.UPDATE_MYSELF;
                var _arrMsg = {
                    type: CODE.USER.BALANCE,
                    msg: "SNG Tournament Prize Awarded",
                    balance: updateResult.balance,
                    prize_amount: rewardAmount,
                    rank: rank
                };
                var _code = CODE.USER.UPDATE_MONEY;
                messageService.pushMessageByUid(uid, _cmdPush, _arrMsg, _code);

            } else {
                logger.error("[awardPlayerPrize] Error updating player balance:", updateErr);
            }
        });

    } catch (error) {
        logger.error("[awardPlayerPrize] Error awarding prize:", error);
    }
};
