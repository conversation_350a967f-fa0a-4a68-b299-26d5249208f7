const { Model } = require('sequelize');

'use strict';

module.exports = (sequelize, DataTypes) => {
  class SngTournamentPlayer extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      SngTournamentPlayer.belongsTo(models.SngTournament, {
        foreignKey: 'tournament_id',
        as: 'tournament'
      });
      
      SngTournamentPlayer.belongsTo(models.Player, {
        foreignKey: 'player_id',
        as: 'player'
      });
    }
  }

  SngTournamentPlayer.init({
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      primaryKey: true,
      autoIncrement: true
    },
    tournament_id: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      references: {
        model: 'sng_tournaments',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    player_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'players',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    seat_number: {
      type: DataTypes.TINYINT,
      allowNull: true
    },
    initial_chips: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    },
    current_chips: {
      type: DataTypes.INTEGER.UNSIGNED,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('ACTIVE', 'ELIMINATED', 'WINNER'),
      defaultValue: 'ACTIVE'
    },
    eliminated_at_hand: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    rank: {
      type: DataTypes.TINYINT,
      allowNull: true
    },
    joined_at: {
      type: DataTypes.DATE,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP')
    }
  }, {
    sequelize,
    modelName: 'SngTournamentPlayer',
    tableName: 'sng_tournament_players',
    timestamps: false
  });

  return SngTournamentPlayer;
};
