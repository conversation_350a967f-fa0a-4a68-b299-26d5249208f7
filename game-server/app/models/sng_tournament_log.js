const { Model } = require('sequelize');

'use strict';

module.exports = (sequelize, DataTypes) => {
  class SngTournamentLog extends Model {
    /**
     * Helper method for defining associations.
     * This method is not a part of Sequelize lifecycle.
     * The `models/index` file will call this method automatically.
     */
    static associate(models) {
      // define association here
      SngTournamentLog.belongsTo(models.SngTournament, {
        foreignKey: 'tournament_id',
        as: 'tournament'
      });
      
      SngTournamentLog.belongsTo(models.Player, {
        foreignKey: 'player_id',
        as: 'player'
      });
    }
  }

  SngTournamentLog.init({
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      primaryKey: true,
      autoIncrement: true
    },
    tournament_id: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
      references: {
        model: 'sng_tournaments',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    player_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'players',
        key: 'id'
      },
      onDelete: 'CASCADE'
    },
    action_type: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    data: {
      type: DataTypes.JSON,
      allowNull: true
    },
    amount: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: sequelize.literal('CURRENT_TIMESTAMP')
    }
  }, {
    sequelize,
    modelName: 'SngTournamentLog',
    tableName: 'sng_tournament_logs',
    timestamps: false
  });

  return SngTournamentLog;
};
